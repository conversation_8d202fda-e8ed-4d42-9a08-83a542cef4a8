[gd_scene load_steps=9 format=3 uid="uid://c85c8s2c5yypg"]

[ext_resource type="Script" uid="uid://cif1twj0ad5n" path="res://scenes/mapObjects/Chest2.cs" id="1_chest2_script"]
[ext_resource type="Texture2D" uid="uid://k7p0aoam6uxm" path="res://resources/solaria/buildings/animated/Chest 09-Sheet.png" id="1_hkfav"]
[ext_resource type="Texture2D" uid="uid://bixtqj6sjwuny" path="res://resources/solaria/crypt/chest_stone.png" id="3_l4trm"]

[sub_resource type="Animation" id="Animation_6w65k"]
resource_name = "OpenChest"
length = 0.4
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}

[sub_resource type="Animation" id="Animation_kikse"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_01vf8"]
_data = {
&"OpenChest": SubResource("Animation_6w65k"),
&"RESET": SubResource("Animation_kikse")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_hkfav"]
radius = 16.0312

[sub_resource type="RectangleShape2D" id="RectangleShape2D_hkfav"]
size = Vector2(16, 9)

[node name="Chest2" type="Node2D"]
script = ExtResource("1_chest2_script")
WoodenChestTexture = ExtResource("1_hkfav")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("3_l4trm")
hframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_01vf8")
}

[node name="PlayerDetection" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetection"]
shape = SubResource("CircleShape2D_hkfav")

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 3.5)
shape = SubResource("RectangleShape2D_hkfav")

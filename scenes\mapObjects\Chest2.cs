using Godot;
using System.Collections.Generic;

public partial class Chest2 : Node2D
{
	public enum ChestType
	{
		WoodenChest = 0,
		StoneChest = 1
	}

	[Export] public ChestType CurrentChestType { get; set; } = ChestType.WoodenChest;
	[Export] public Texture2D WoodenChestTexture { get; set; }
	[Export] public Texture2D StoneChestTexture { get; set; }
	[Export] public int GoldAmount { get; set; } = 50;
	public List<ResourceReward> ResourceRewards { get; set; } = new List<ResourceReward>();

	public string Id { get; set; } = System.Guid.NewGuid().ToString();

	private Sprite2D _sprite;
	private AnimationPlayer _animationPlayer;
	private Area2D _playerDetection;
	private bool _isPlayerInRange = false;
	private bool _isOpened = false;

	public override void _Ready()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_playerDetection = GetNode<Area2D>("PlayerDetection");

		if (_sprite == null)
		{
			GD.PrintErr("Chest2: Sprite2D node not found!");
			return;
		}

		if (_animationPlayer == null)
		{
			GD.PrintErr("Chest2: AnimationPlayer node not found!");
			return;
		}

		if (_playerDetection == null)
		{
			GD.PrintErr("Chest2: PlayerDetection node not found!");
			return;
		}

		SetupPlayerDetection();
		SetChestTexture();

		RegisterWithGameData();
	}

	public override void _ExitTree()
	{
		if (_playerDetection != null)
		{
			_playerDetection.AreaEntered -= OnPlayerEntered;
			_playerDetection.AreaExited -= OnPlayerExited;
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (_isPlayerInRange && !_isOpened && @event.IsActionPressed("Interact"))
		{
			if (TryOpenChest())
			{
				GetViewport().SetInputAsHandled();
			}
		}
	}

	private void SetupPlayerDetection()
	{
		_playerDetection.CollisionMask = 4;
		_playerDetection.AreaEntered += OnPlayerEntered;
		_playerDetection.AreaExited += OnPlayerExited;
	}

	private void SetChestTexture()
	{
		if (_sprite == null) return;

		switch (CurrentChestType)
		{
			case ChestType.WoodenChest:
				if (WoodenChestTexture != null)
					_sprite.Texture = WoodenChestTexture;
				break;
			case ChestType.StoneChest:
				if (StoneChestTexture != null)
					_sprite.Texture = StoneChestTexture;
				break;
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			GD.Print("Chest2: Player in range - press 'R' to open");
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
		}
	}

	private bool TryOpenChest()
	{
		if (_isOpened) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
		{
			GD.PrintErr("Chest2: ResourcesManager not found!");
			return false;
		}

		ResourceType requiredKey = GetRequiredKey();
		if (!resourcesManager.HasResource(requiredKey, 1))
		{
			GD.Print($"Chest2: Player lacks required key: {requiredKey}");
			return false;
		}

		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		resourcesManager.RemoveResource(requiredKey, 1);
		GD.Print($"Chest2: Used {requiredKey} to open chest");

		OpenChest();
		return true;
	}

	private ResourceType GetRequiredKey()
	{
		return ResourceType.CopperKey;
	}

	private void OpenChest()
	{
		_isOpened = true;
		GD.Print($"Chest2: Opening {CurrentChestType} chest!");

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("OpenChest");
			_animationPlayer.AnimationFinished += OnOpenAnimationFinished;
		}
		else
		{
			OnOpenAnimationFinished("");
		}
	}

	private void OnOpenAnimationFinished(StringName animationName)
	{
		if (_animationPlayer != null)
		{
			_animationPlayer.AnimationFinished -= OnOpenAnimationFinished;
		}

		SpawnGold();
		SpawnResources();

		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		GetTree().CreateTimer(2.0f).Timeout += DestroyChest;
	}

	private void SpawnGold()
	{
		if (GoldAmount <= 0) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			resourcesManager.AddResource(ResourceType.GoldBar, GoldAmount);
			GD.Print($"Chest2: Added {GoldAmount} gold to player inventory");
		}
	}

	private void SpawnResources()
	{
		foreach (var reward in ResourceRewards)
		{
			for (int i = 0; i < reward.Quantity; i++)
			{
				Vector2 offset = new Vector2(
					(float)(GD.Randf() - 0.5f) * 16.0f,
					(float)(GD.Randf() - 0.5f) * 16.0f
				);
				Vector2 spawnPosition = GlobalPosition + offset;

				DroppedResource.SpawnResource(spawnPosition, reward.ResourceType, 1);
			}
		}
	}

	private void DestroyChest()
	{
		UnregisterFromGameData();
		QueueFree();
	}

	private void RegisterWithGameData()
	{
		var chestData = GetSaveData();
		GameSaveData.Instance.WorldData.Chests.Add(chestData);
		GD.Print($"Chest2: Registered chest {Id} with GameData");
	}

	private void UnregisterFromGameData()
	{
		int removedCount = GameSaveData.Instance.WorldData.Chests.RemoveAll(c => c.Id == Id);
		GD.Print($"Chest2: Unregistered chest {Id} from GameData (removed: {removedCount})");
	}

	public ChestSaveData GetSaveData()
	{
		return new ChestSaveData
		{
			Id = Id,
			X = GlobalPosition.X,
			Y = GlobalPosition.Y,
			ChestType = (int)CurrentChestType,
			GoldAmount = GoldAmount,
			ResourceRewards = new List<ResourceReward>(ResourceRewards),
			IsOpened = _isOpened
		};
	}

	public void LoadFromSaveData(ChestSaveData saveData)
	{
		Id = saveData.Id;
		GlobalPosition = new Vector2(saveData.X, saveData.Y);
		CurrentChestType = (ChestType)saveData.ChestType;
		GoldAmount = saveData.GoldAmount;
		ResourceRewards = new List<ResourceReward>(saveData.ResourceRewards);
		_isOpened = saveData.IsOpened;

		SetChestTexture();

		if (_isOpened)
		{
			QueueFree();
		}
	}

	public void SetRewards(int goldAmount, List<ResourceReward> resourceRewards)
	{
		GoldAmount = goldAmount;
		ResourceRewards = new List<ResourceReward>(resourceRewards);
	}
}

using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class Region5Manager : Node2D
{
	[Export] public float BaseSpawnInterval { get; set; } = 15.0f; // Base 15 seconds
	[Export] public int RegionId { get; set; } = 5;
	[Export] public int InitiallySpawnedObjects { get; set; } = 15;
	[Export] public int MaxItemsSpawned { get; set; } = 50;

	// Object spawn weights (higher = more likely to spawn)
	[Export] public int Tree2SpawnWeight { get; set; } = 25; // Reduced by 5 for Trunk
	[Export] public int CopperRockSpawnWeight { get; set; } = 15;
	[Export] public int GreenBush2SpawnWeight { get; set; } = 15;
	[Export] public int BrownMushroomSpawnWeight { get; set; } = 15;
	[Export] public int RockSpawnWeight { get; set; } = 10;
	[Export] public int Rock2SpawnWeight { get; set; } = 10;
	[Export] public int BerryBushSpawnWeight { get; set; } = 5;
	[Export] public int TrunkSpawnWeight { get; set; } = 5;

	// Scene references
	[Export] public PackedScene Tree2Scene { get; set; }
	[Export] public PackedScene CopperRockScene { get; set; }
	[Export] public PackedScene GreenBush2Scene { get; set; }
	[Export] public PackedScene BrownMushroomScene { get; set; }
	[Export] public PackedScene RockScene { get; set; }
	[Export] public PackedScene Rock2Scene { get; set; }
	[Export] public PackedScene BerryBushScene { get; set; }
	[Export] public PackedScene TrunkScene { get; set; }

	// Enemy spawning configuration
	[Export] public int MaxEnemiesPerRegion { get; set; } = 2;
	[Export] public float EnemySpawnInterval { get; set; } = 120.0f; // 2 minutes
	[Export] public PackedScene GoblinScene { get; set; }

	// Internal state
	private Timer _spawnTimer;
	private Timer _enemySpawnTimer;
	private Random _random = new();
	private bool _isRegionUnlocked = false;
	private CustomDataLayerManager _customDataManager;
	private Dictionary<Vector2I, Node2D> _activeObjects = new();
	private Dictionary<Vector2I, int> _objectHealthData = new();
	private List<BaseEnemy> _activeEnemies = new();
	private List<EnemyTerritory> _territories = new();

	public override void _Ready()
	{
		CheckRegionUnlockStatus();

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		// Always connect to region unlock signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked += OnRegionUnlocked;
			CommonSignals.Instance.EnemyDefeated += OnEnemyDefeated;
		}

		// Always load existing data regardless of unlock status
		CallDeferred(nameof(CleanCorruptedData));
		CallDeferred(nameof(LoadExistingObjects));
		CallDeferred(nameof(LoadExistingEnemies));
		CallDeferred(nameof(LoadObjectHealthData));

		// Find territory areas in RegionSpecific/Region5
		CallDeferred(nameof(FindTerritoryAreas));

		// Always do initial spawn (for visibility when region unlocks)
		CallDeferred(nameof(FirstTimeInitialize));

		// StartOngoingSpawning will be called after LoadExistingObjects completes (if region is unlocked)
	}

	private void StartOngoingSpawning()
	{
		_spawnTimer = new Timer();
		_spawnTimer.WaitTime = CalculateNextSpawnInterval();
		_spawnTimer.OneShot = true;
		_spawnTimer.Autostart = true;
		_spawnTimer.Timeout += OnSpawnTimer;
		AddChild(_spawnTimer);

		// Setup enemy spawn timer
		_enemySpawnTimer = new Timer();
		_enemySpawnTimer.WaitTime = EnemySpawnInterval;
		_enemySpawnTimer.OneShot = true;
		_enemySpawnTimer.Autostart = false;
		_enemySpawnTimer.Timeout += OnEnemySpawnTimer;
		AddChild(_enemySpawnTimer);
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked -= OnRegionUnlocked;
			CommonSignals.Instance.EnemyDefeated -= OnEnemyDefeated;
		}
		base._ExitTree();
	}

	private void CheckRegionUnlockStatus()
	{
		_isRegionUnlocked = GameSaveData.Instance.UnlockedRegions?.Contains(RegionId) ?? false;
	}

	private void OnRegionUnlocked(int regionId)
	{
		if (regionId == RegionId && !_isRegionUnlocked)
		{
			_isRegionUnlocked = true;
			StartOngoingSpawning();
		}
	}

	private void OnEnemyDefeated(EnemyType enemyType, Vector2 position, int xpReward)
	{
		// Clean up dead enemies and restart spawn timer if needed
		int removedCount = _activeEnemies.RemoveAll(enemy => !IsInstanceValid(enemy));

		GD.Print($"Region{RegionId}Manager: Enemy defeated! Removed {removedCount} dead enemies. Active: {_activeEnemies.Count}/{MaxEnemiesPerRegion}");

		// Update save data
		UpdateEnemiesInGameData();

		// Restart spawn timer if we're below the limit
		if (_activeEnemies.Count < MaxEnemiesPerRegion && _enemySpawnTimer != null && _enemySpawnTimer.IsStopped())
		{
			_enemySpawnTimer.WaitTime = EnemySpawnInterval;
			_enemySpawnTimer.Start();
			GD.Print($"Region{RegionId}Manager: Restarted enemy spawn timer ({_activeEnemies.Count}/{MaxEnemiesPerRegion} enemies)");
		}
	}

	public void FirstTimeInitialize()
	{
		if (GameSaveData.Instance.FirstTimeInitializedRegions.Contains(RegionId)) return;

		for (var i = 0; i < InitiallySpawnedObjects; i++) TrySpawnObject();
		GameSaveData.Instance.FirstTimeInitializedRegions.Add(RegionId);
	}

	private void OnSpawnTimer()
	{
		TrySpawnObject();

		_spawnTimer.WaitTime = CalculateNextSpawnInterval();
		_spawnTimer.Start();
	}

	private void OnEnemySpawnTimer()
	{
		TrySpawnEnemy();

		// Restart timer for next enemy spawn
		_enemySpawnTimer.WaitTime = EnemySpawnInterval;
		_enemySpawnTimer.Start();
	}

	private float CalculateNextSpawnInterval()
	{
		// Add some randomness to spawn intervals
		float randomFactor = (float)_random.NextDouble() * 0.4f + 0.8f; // 0.8 to 1.2
		return BaseSpawnInterval * randomFactor;
	}

	private void TrySpawnObject()
	{
		if (!_isRegionUnlocked) return;

		// Check if we've reached the maximum number of spawned objects
		if (_activeObjects.Count >= MaxItemsSpawned)
		{
			GD.Print($"Region{RegionId}Manager: Maximum objects reached ({_activeObjects.Count}/{MaxItemsSpawned})");
			return;
		}

		// Find a valid spawn position
		var validPositions = GetValidSpawnPositions();
		if (validPositions.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No valid spawn positions available");
			return;
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		var objectType = DetermineObjectTypeToSpawn();
		SpawnObjectAt(spawnPosition, objectType);
	}

	private void TrySpawnEnemy()
	{
		// Clean up dead enemies first
		int removedCount = _activeEnemies.RemoveAll(enemy => !IsInstanceValid(enemy));
		if (removedCount > 0)
		{
			UpdateEnemiesInGameData();
		}

		// Check if we're under the limit
		if (_activeEnemies.Count >= MaxEnemiesPerRegion)
		{
			GD.Print($"Region{RegionId}Manager: Enemy limit reached ({_activeEnemies.Count}/{MaxEnemiesPerRegion})");
			return;
		}

		// Find a random valid position for enemy spawning
		var validPositions = GetValidEnemySpawnPositions();
		if (validPositions.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No valid enemy spawn positions available");
			return;
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		SpawnEnemyAt(spawnPosition, EnemyType.Goblin);
	}

	private List<Vector2I> GetValidSpawnPositions()
	{
		var validPositions = new List<Vector2I>();

		// Get all tiles in this region where objects can be spawned
		var tiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanDestroyableObject &&
			tile.ObjectTypePlaced == ObjectTypePlaced.None);

		foreach (var tile in tiles)
		{
			validPositions.Add(tile.Position);
		}

		return validPositions;
	}

	private List<Vector2I> GetValidEnemySpawnPositions()
	{
		var validPositions = new List<Vector2I>();
		const float MinDistanceBetweenEnemies = 64.0f; // Minimum distance in pixels (4 tiles)

		// Get all tiles in this region where enemies can be spawned
		var tiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanEnemy &&
			tile.ObjectTypePlaced == ObjectTypePlaced.None);

		foreach (var tile in tiles)
		{
			Vector2 worldPosition = new Vector2(tile.Position.X * 16 + 8, tile.Position.Y * 16 + 8);

			// Check if this position is too close to existing enemies
			bool tooCloseToExistingEnemy = false;
			foreach (var enemy in _activeEnemies)
			{
				if (IsInstanceValid(enemy))
				{
					float distance = worldPosition.DistanceTo(enemy.GlobalPosition);
					if (distance < MinDistanceBetweenEnemies)
					{
						tooCloseToExistingEnemy = true;
						break;
					}
				}
			}

			if (!tooCloseToExistingEnemy)
			{
				validPositions.Add(tile.Position);
			}
		}

		GD.Print($"Region{RegionId}Manager: Found {validPositions.Count} valid enemy spawn positions (filtered for distance)");
		return validPositions;
	}

	private ObjectType DetermineObjectTypeToSpawn()
	{
		int totalWeight = Tree2SpawnWeight + CopperRockSpawnWeight + GreenBush2SpawnWeight +
						 BrownMushroomSpawnWeight + RockSpawnWeight + Rock2SpawnWeight + BerryBushSpawnWeight + TrunkSpawnWeight;
		int randomValue = _random.Next(totalWeight);

		int currentWeight = 0;
		if (randomValue < (currentWeight += Tree2SpawnWeight))
			return ObjectType.Tree; // Will spawn Tree2 instead
		if (randomValue < (currentWeight += CopperRockSpawnWeight))
			return ObjectType.CopperRock;
		if (randomValue < (currentWeight += GreenBush2SpawnWeight))
			return ObjectType.GreenBush; // Will spawn GreenBush2 instead
		if (randomValue < (currentWeight += BrownMushroomSpawnWeight))
			return ObjectType.BrownMushroom;
		if (randomValue < (currentWeight += RockSpawnWeight))
			return ObjectType.Rock;
		if (randomValue < (currentWeight += Rock2SpawnWeight))
			return ObjectType.Rock2;
		if (randomValue < (currentWeight += TrunkSpawnWeight))
			return ObjectType.Trunk;

		return ObjectType.BerryBush;
	}

	private void SpawnObjectAt(Vector2I tilePosition, ObjectType objectType)
	{
		Node2D objectInstance = null;

		switch (objectType)
		{
			case ObjectType.Tree:
				objectInstance = SpawnTree(tilePosition); // Spawns Tree2
				break;
			case ObjectType.Rock:
				objectInstance = SpawnRock(tilePosition);
				break;
			case ObjectType.BerryBush:
				objectInstance = SpawnBerryBush(tilePosition);
				break;
			case ObjectType.GreenBush:
				objectInstance = SpawnGreenBush(tilePosition); // Spawns GreenBush2
				break;
			case ObjectType.Rock2:
				objectInstance = SpawnRock2(tilePosition);
				break;
			case ObjectType.CopperRock:
				objectInstance = SpawnCopperRock(tilePosition);
				break;
			case ObjectType.BrownMushroom:
				objectInstance = SpawnMushroom(tilePosition);
				break;
			case ObjectType.Trunk:
				objectInstance = SpawnTrunk(tilePosition);
				break;
		}

		if (objectInstance != null)
		{
			// Track object
			_activeObjects[tilePosition] = objectInstance;
			UpdateObjectHealthInGameData();

			// Mark tile as occupied
			ObjectTypePlaced placedType = objectType switch
			{
				ObjectType.Tree => ObjectTypePlaced.Tree,
				ObjectType.Rock => ObjectTypePlaced.Rock,
				ObjectType.BerryBush => ObjectTypePlaced.BerryBush,
				ObjectType.GreenBush => ObjectTypePlaced.BerryBush,
				ObjectType.Rock2 => ObjectTypePlaced.Rock2,
				ObjectType.Trunk => ObjectTypePlaced.Rock,
				_ => ObjectTypePlaced.None
			};
			_customDataManager.SetObjectPlaced(tilePosition, placedType);

			// Save the updated custom layer data immediately
			SaveCustomLayerDataToGameData();
		}
	}

	private void SpawnEnemyAt(Vector2I tilePosition, EnemyType enemyType)
	{
		if (GoblinScene == null)
		{
			GD.PrintErr("Region5Manager: GoblinScene is null!");
			return;
		}

		var enemy = GoblinScene.Instantiate<MeleeGoblin>();
		if (enemy == null)
		{
			GD.PrintErr("Region5Manager: Failed to instantiate goblin!");
			return;
		}

		// Set enemy properties
		enemy.SetRegion(RegionId);
		enemy.GlobalPosition = new Vector2(tilePosition.X * 16 + 8, tilePosition.Y * 16 + 8);

		// Find the closest territory and assign enemy to it
		var closestTerritory = FindClosestTerritory(enemy.GlobalPosition);
		if (closestTerritory != null)
		{
			closestTerritory.AssignEnemy(enemy);
			GD.Print($"Region{RegionId}Manager: Assigned goblin to territory at {closestTerritory.GlobalPosition}");
		}
		else
		{
			// Fallback: set territory center to spawn position
			enemy.SetTerritory(enemy.GlobalPosition, 120.0f);
			GD.Print($"Region{RegionId}Manager: No territory found, using spawn position as territory center");
		}

		// Add to scene
		GetParent().CallDeferred("add_child", enemy);

		// Track the enemy
		_activeEnemies.Add(enemy);
		UpdateEnemiesInGameData();

		GD.Print($"Region{RegionId}Manager: Spawned {enemyType} at {tilePosition} (Total: {_activeEnemies.Count}/{MaxEnemiesPerRegion})");
	}

	private Tree2 SpawnTree(Vector2I tilePosition)
	{
		if (Tree2Scene == null) return null;

		var tree = Tree2Scene.Instantiate<Tree2>();
		if (tree == null) return null;

		tree.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", tree);
		tree.Tree2Destroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, tree);

		return tree;
	}

	private Rock SpawnRock(Vector2I tilePosition)
	{
		if (RockScene == null) return null;

		var rock = RockScene.Instantiate<Rock>();
		if (rock == null) return null;

		rock.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", rock);
		rock.RockDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, rock);

		return rock;
	}

	private BerryBush SpawnBerryBush(Vector2I tilePosition)
	{
		if (BerryBushScene == null) return null;

		var berryBush = BerryBushScene.Instantiate<BerryBush>();
		if (berryBush == null) return null;

		berryBush.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", berryBush);
		berryBush.BerryBushDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, berryBush);

		return berryBush;
	}

	private GreenBush2 SpawnGreenBush(Vector2I tilePosition)
	{
		if (GreenBush2Scene == null) return null;

		var greenBush = GreenBush2Scene.Instantiate<GreenBush2>();
		if (greenBush == null) return null;

		greenBush.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", greenBush);
		greenBush.GreenBush2Destroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, greenBush);

		return greenBush;
	}

	private Rock2 SpawnRock2(Vector2I tilePosition)
	{
		if (Rock2Scene == null) return null;

		var rock2 = Rock2Scene.Instantiate<Rock2>();
		if (rock2 == null) return null;

		rock2.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", rock2);
		rock2.Rock2Destroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, rock2);

		return rock2;
	}

	private CopperRock SpawnCopperRock(Vector2I tilePosition)
	{
		if (CopperRockScene == null) return null;

		var copperRock = CopperRockScene.Instantiate<CopperRock>();
		if (copperRock == null) return null;

		copperRock.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", copperRock);
		copperRock.CopperRockDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, copperRock);

		return copperRock;
	}

	private BrownMushroom SpawnMushroom(Vector2I tilePosition)
	{
		if (BrownMushroomScene == null) return null;

		var mushroom = BrownMushroomScene.Instantiate<BrownMushroom>();
		if (mushroom == null) return null;

		mushroom.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", mushroom);
		mushroom.BrownMushroomDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, mushroom);

		return mushroom;
	}

	private Trunk SpawnTrunk(Vector2I tilePosition)
	{
		if (TrunkScene == null)
		{
			TrunkScene = GD.Load<PackedScene>("res://scenes/mapObjects/Trunk.tscn");
			if (TrunkScene == null)
			{
				GD.PrintErr("Region5Manager: Failed to load Trunk scene!");
				return null;
			}
		}

		var trunk = TrunkScene.Instantiate<Trunk>();
		if (trunk == null)
		{
			GD.PrintErr("Region5Manager: Failed to instantiate trunk!");
			return null;
		}

		trunk.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", trunk);
		trunk.TrunkDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, trunk);

		return trunk;
	}

	private void OnObjectDestroyed(Vector2I tilePosition)
	{
		if (_activeObjects.ContainsKey(tilePosition))
		{
			_activeObjects.Remove(tilePosition);
			_objectHealthData.Remove(tilePosition);
			UpdateObjectHealthInGameData();
		}
	}

	private void RestoreObjectHealth(Vector2I tilePosition, IDestroyableObject obj)
	{
		if (_objectHealthData.TryGetValue(tilePosition, out int savedHealth))
		{
			obj.SetCurrentHealth(savedHealth);
			GD.Print($"Region{RegionId}Manager: Restored health {savedHealth} for object at {tilePosition}");
		}
	}

	private void CleanCorruptedData()
	{
		// Implementation similar to other region managers
		// Clean up any corrupted save data
	}

	private void LoadExistingObjects()
	{
		// Implementation similar to other region managers
		// Load objects from save data
		if (_isRegionUnlocked)
		{
			StartOngoingSpawning();
		}
	}

	private void LoadExistingEnemies()
	{
		_activeEnemies.Clear();

		// Load enemy save data from ResourcesManager
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		var enemySaveDataList = resourcesManager.LoadEnemyData(RegionId);
		if (enemySaveDataList == null || enemySaveDataList.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No saved enemies found for region {RegionId}");
			
			// Start enemy spawn timer after loading (only if below max limit)
			if (_enemySpawnTimer != null && _activeEnemies.Count < MaxEnemiesPerRegion)
			{
				_enemySpawnTimer.Start();
				GD.Print($"Region{RegionId}Manager: Started enemy spawn timer ({_activeEnemies.Count}/{MaxEnemiesPerRegion} enemies)");
			}
			return;
		}

		GD.Print($"Region{RegionId}Manager: Loading {enemySaveDataList.Count} saved enemies");

		foreach (var saveData in enemySaveDataList)
		{
			if (GoblinScene == null) continue;

			var enemy = GoblinScene.Instantiate<MeleeGoblin>();
			if (enemy == null) continue;

			// Add to scene first
			GetParent().AddChild(enemy);

			// Now load save data after the enemy is in the scene tree
			enemy.LoadFromSaveData(saveData);

			_activeEnemies.Add(enemy);
		}

		GD.Print($"Region{RegionId}Manager: Loaded {_activeEnemies.Count} enemies for region {RegionId}");

		// Start enemy spawn timer after loading existing enemies (only if below max limit)
		if (_enemySpawnTimer != null && _activeEnemies.Count < MaxEnemiesPerRegion)
		{
			_enemySpawnTimer.Start();
			GD.Print($"Region{RegionId}Manager: Started enemy spawn timer ({_activeEnemies.Count}/{MaxEnemiesPerRegion} enemies)");
		}
	}

	private void LoadObjectHealthData()
	{
		// Implementation similar to other region managers
	}

	private void UpdateObjectHealthInGameData()
	{
		// Collect current health from all active objects
		_objectHealthData.Clear();

		foreach (var kvp in _activeObjects)
		{
			var position = kvp.Key;
			var obj = kvp.Value;

			if (obj is IDestroyableObject destroyableObj)
			{
				_objectHealthData[position] = destroyableObj.GetCurrentHealth();
			}
		}

		// Update GameData directly
		string key = $"object_health_region_{RegionId}";
		GameSaveData.Instance.WorldData.CustomLayerData[key] = _objectHealthData;

		GD.Print($"Region{RegionId}Manager: Updated health data for {_objectHealthData.Count} objects in GameData");
	}

	private void UpdateEnemiesInGameData()
	{
		// Clean up dead enemies first
		_activeEnemies.RemoveAll(enemy => !IsInstanceValid(enemy));

		var enemySaveDataList = new List<EnemySaveData>();

		foreach (var enemy in _activeEnemies)
		{
			if (enemy.GetRegion() == RegionId)
			{
				enemySaveDataList.Add(enemy.GetSaveData());
			}
		}

		// Update GameData directly
		string key = $"enemies_region_{RegionId}";
		GameSaveData.Instance.WorldData.CustomLayerData[key] = enemySaveDataList;

		GD.Print($"Region{RegionId}Manager: Updated {enemySaveDataList.Count} enemies for region {RegionId} in GameData");
	}

	private void SaveCustomLayerDataToGameData()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null && _customDataManager != null)
		{
			resourcesManager.SaveCustomLayerData(_customDataManager);
		}
	}

	private void FindTerritoryAreas()
	{
		_territories.Clear();

		// Find RegionSpecific/Region5 node
		var regionSpecific = GetNode<Node2D>("/root/world/RegionSpecific");
		if (regionSpecific == null)
		{
			GD.PrintErr("Region5Manager: RegionSpecific node not found!");
			return;
		}

		var region5Node = regionSpecific.GetNode<Node2D>("Region5");
		if (region5Node == null)
		{
			GD.PrintErr("Region5Manager: Region5 node not found in RegionSpecific!");
			return;
		}

		// Find all EnemyTerritory children
		foreach (Node child in region5Node.GetChildren())
		{
			if (child is EnemyTerritory territory)
			{
				_territories.Add(territory);
				GD.Print($"Region5Manager: Found territory at {territory.GlobalPosition}");
			}
		}

		GD.Print($"Region5Manager: Found {_territories.Count} territory areas");
	}

	private EnemyTerritory FindClosestTerritory(Vector2 position)
	{
		if (_territories.Count == 0) return null;

		EnemyTerritory closest = null;
		float closestDistance = float.MaxValue;

		foreach (var territory in _territories)
		{
			float distance = position.DistanceTo(territory.GlobalPosition);
			if (distance < closestDistance)
			{
				closestDistance = distance;
				closest = territory;
			}
		}

		return closest;
	}

	private EnemyTerritory FindAvailableTerritory()
	{
		// Find territory with least enemies assigned
		EnemyTerritory bestTerritory = null;
		int minEnemies = int.MaxValue;

		foreach (var territory in _territories)
		{
			int enemyCount = territory.GetAssignedEnemies().Count;
			if (enemyCount < minEnemies)
			{
				minEnemies = enemyCount;
				bestTerritory = territory;
			}
		}

		return bestTerritory;
	}
}

using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class Region6Manager : Node2D
{
	[Export] public int RegionId { get; set; } = 6;
	[Export] public int InitiallySpawnedObjects { get; set; } = 4;
	[Export] public int MaxItemsSpawned { get; set; } = 24;

	// Object spawn weights (higher = more likely to spawn)
	[Export] public int Tree2SpawnWeight { get; set; } = 25;
	[Export] public int CopperRockSpawnWeight { get; set; } = 15;
	[Export] public int GreenBush2SpawnWeight { get; set; } = 15;
	[Export] public int BrownMushroomSpawnWeight { get; set; } = 15;
	[Export] public int RockSpawnWeight { get; set; } = 10;
	[Export] public int Rock2SpawnWeight { get; set; } = 10;
	[Export] public int BerryBushSpawnWeight { get; set; } = 5;
	[Export] public int TrunkSpawnWeight { get; set; } = 5;

	// Scene references
	[Export] public PackedScene Tree2Scene { get; set; }
	[Export] public PackedScene CopperRockScene { get; set; }
	[Export] public PackedScene GreenBush2Scene { get; set; }
	[Export] public PackedScene BrownMushroomScene { get; set; }
	[Export] public PackedScene RockScene { get; set; }
	[Export] public PackedScene Rock2Scene { get; set; }
	[Export] public PackedScene BerryBushScene { get; set; }
	[Export] public PackedScene TrunkScene { get; set; }

	// Spawning configuration
	[Export] public float SpawnInterval { get; set; } = 30.0f;

	// Chest spawning
	private bool _chestsSpawned = false;

	private CustomDataLayerManager _customDataManager;
	private Dictionary<Vector2I, Node2D> _activeObjects = new();
	private Dictionary<Vector2I, int> _objectHealthData = new();
	private Random _random = new();
	private Timer _spawnTimer;
	private bool _isRegionUnlocked = false;

	public override void _Ready()
	{
		CheckRegionUnlockStatus();

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked += OnRegionUnlocked;
			CommonSignals.Instance.GraveyardCandleInteracted += OnGraveyardCandleInteracted;
		}

		CallDeferred(nameof(CleanCorruptedData));
		CallDeferred(nameof(LoadExistingObjects));
		CallDeferred(nameof(LoadObjectHealthData));

		CallDeferred(nameof(FirstTimeInitialize));
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked -= OnRegionUnlocked;
			CommonSignals.Instance.GraveyardCandleInteracted -= OnGraveyardCandleInteracted;
		}
	}

	private void CheckRegionUnlockStatus()
	{
		_isRegionUnlocked = GameSaveData.Instance.UnlockedRegions?.Contains(RegionId) ?? false;
	}

	private void OnRegionUnlocked(int regionId)
	{
		if (regionId == RegionId && !_isRegionUnlocked)
		{
			_isRegionUnlocked = true;
			StartOngoingSpawning();
		}
	}

	public void FirstTimeInitialize()
	{
		if(GameSaveData.Instance.FirstTimeInitializedRegions.Contains(RegionId)) return;

		for(var i = 0; i < InitiallySpawnedObjects; i++) TrySpawnObject();
		GameSaveData.Instance.FirstTimeInitializedRegions.Add(RegionId);
	}

	private void StartOngoingSpawning()
	{
		if (_spawnTimer != null) return;

		_spawnTimer = new Timer();
		_spawnTimer.WaitTime = SpawnInterval;
		_spawnTimer.Timeout += OnSpawnTimer;
		AddChild(_spawnTimer);
		_spawnTimer.Start();

		GD.Print($"Region{RegionId}Manager: Started ongoing spawning with interval {SpawnInterval}s");
	}

	private void OnSpawnTimer()
	{
		TrySpawnObject();

		_spawnTimer.WaitTime = SpawnInterval;
		_spawnTimer.Start();
	}

	private void TrySpawnObject(ObjectType? objectType = null)
	{
		if (!_isRegionUnlocked) return;

		if (_activeObjects.Count >= MaxItemsSpawned)
		{
			GD.Print($"Region{RegionId}Manager: Maximum objects reached ({_activeObjects.Count}/{MaxItemsSpawned})");
			return;
		}

		var validPositions = GetValidSpawnPositions();
		if (validPositions.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No valid spawn positions available");
			return;
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		var selectedObjectType = objectType ?? DetermineObjectTypeToSpawn();
		SpawnObjectAt(spawnPosition, selectedObjectType);
	}

	private List<Vector2I> GetValidSpawnPositions()
	{
		var validPositions = new List<Vector2I>();

		var tiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanDestroyableObject &&
			tile.ObjectTypePlaced == ObjectTypePlaced.None);

		foreach (var tile in tiles)
		{
			validPositions.Add(tile.Position);
		}

		return validPositions;
	}

	private ObjectType DetermineObjectTypeToSpawn()
	{
		int totalWeight = Tree2SpawnWeight + CopperRockSpawnWeight + GreenBush2SpawnWeight +
						 BrownMushroomSpawnWeight + RockSpawnWeight + Rock2SpawnWeight + BerryBushSpawnWeight + TrunkSpawnWeight;
		int randomValue = _random.Next(totalWeight);

		int currentWeight = 0;
		if (randomValue < (currentWeight += Tree2SpawnWeight))
			return ObjectType.Tree;
		if (randomValue < (currentWeight += CopperRockSpawnWeight))
			return ObjectType.CopperRock;
		if (randomValue < (currentWeight += GreenBush2SpawnWeight))
			return ObjectType.GreenBush;
		if (randomValue < (currentWeight += BrownMushroomSpawnWeight))
			return ObjectType.BrownMushroom;
		if (randomValue < (currentWeight += RockSpawnWeight))
			return ObjectType.Rock;
		if (randomValue < (currentWeight += Rock2SpawnWeight))
			return ObjectType.Rock2;
		if (randomValue < (currentWeight += TrunkSpawnWeight))
			return ObjectType.Trunk;

		return ObjectType.BerryBush;
	}

	private void SpawnObjectAt(Vector2I tilePosition, ObjectType objectType)
	{
		Node2D objectInstance = null;

		switch (objectType)
		{
			case ObjectType.Tree:
				objectInstance = SpawnTree(tilePosition);
				break;
			case ObjectType.Rock:
				objectInstance = SpawnRock(tilePosition);
				break;
			case ObjectType.BerryBush:
				objectInstance = SpawnBerryBush(tilePosition);
				break;
			case ObjectType.GreenBush:
				objectInstance = SpawnGreenBush(tilePosition);
				break;
			case ObjectType.Rock2:
				objectInstance = SpawnRock2(tilePosition);
				break;
			case ObjectType.CopperRock:
				objectInstance = SpawnCopperRock(tilePosition);
				break;
			case ObjectType.BrownMushroom:
				objectInstance = SpawnMushroom(tilePosition);
				break;
			case ObjectType.Trunk:
				objectInstance = SpawnTrunk(tilePosition);
				break;
		}

		if (objectInstance != null)
		{
			_activeObjects[tilePosition] = objectInstance;
			UpdateObjectHealthInGameData();

			ObjectTypePlaced placedType = objectType switch
			{
				ObjectType.Tree => ObjectTypePlaced.Tree,
				ObjectType.Rock => ObjectTypePlaced.Rock,
				ObjectType.BerryBush => ObjectTypePlaced.BerryBush,
				ObjectType.GreenBush => ObjectTypePlaced.BerryBush,
				ObjectType.Rock2 => ObjectTypePlaced.Rock2,
				ObjectType.Trunk => ObjectTypePlaced.Rock,
				_ => ObjectTypePlaced.None
			};
			_customDataManager.SetObjectPlaced(tilePosition, placedType);

			SaveCustomLayerDataToGameData();
		}
	}

	private void CleanCorruptedData()
	{
		var corruptedPositions = new List<Vector2I>();

		foreach (var kvp in _activeObjects)
		{
			if (!IsInstanceValid(kvp.Value))
			{
				corruptedPositions.Add(kvp.Key);
			}
		}

		foreach (var position in corruptedPositions)
		{
			_activeObjects.Remove(position);
			_objectHealthData.Remove(position);
		}

		if (corruptedPositions.Count > 0)
		{
			GD.Print($"Region{RegionId}Manager: Cleaned {corruptedPositions.Count} corrupted object references");
			UpdateObjectHealthInGameData();
		}
	}

	private void LoadExistingObjects()
	{
		var tiles = _customDataManager.GetTilesWhere(tile => 
			tile.Region == RegionId && 
			tile.ObjectTypePlaced != ObjectTypePlaced.None);

		foreach (var tile in tiles)
		{
			if (_activeObjects.ContainsKey(tile.Position)) continue;

			Node2D objectInstance = tile.ObjectTypePlaced switch
			{
				ObjectTypePlaced.Tree => SpawnTree(tile.Position),
				ObjectTypePlaced.Rock => SpawnRock(tile.Position),
				ObjectTypePlaced.Rock2 => SpawnRock2(tile.Position),
				ObjectTypePlaced.BerryBush => SpawnBerryBush(tile.Position),
				_ => null
			};

			if (objectInstance != null)
			{
				_activeObjects[tile.Position] = objectInstance;
			}
		}

		GD.Print($"Region{RegionId}Manager: Loaded {_activeObjects.Count} existing objects");

		if (_isRegionUnlocked)
		{
			StartOngoingSpawning();
		}
	}

	private void LoadObjectHealthData()
	{
		string key = $"object_health_region_{RegionId}";
		if (GameSaveData.Instance.WorldData.CustomLayerData.ContainsKey(key))
		{
			var savedData = GameSaveData.Instance.WorldData.CustomLayerData[key];
			if (savedData is Dictionary<Vector2I, int> healthData)
			{
				_objectHealthData = new Dictionary<Vector2I, int>(healthData);
				GD.Print($"Region{RegionId}Manager: Loaded health data for {_objectHealthData.Count} objects");
			}
		}
	}

	private void UpdateObjectHealthInGameData()
	{
		// Collect current health from all active objects
		_objectHealthData.Clear();

		foreach (var kvp in _activeObjects)
		{
			var position = kvp.Key;
			var obj = kvp.Value;

			if (obj is IDestroyableObject destroyableObj)
			{
				_objectHealthData[position] = destroyableObj.GetCurrentHealth();
			}
		}

		// Update GameData directly
		string key = $"object_health_region_{RegionId}";
		GameSaveData.Instance.WorldData.CustomLayerData[key] = _objectHealthData;

		GD.Print($"Region{RegionId}Manager: Updated health data for {_objectHealthData.Count} objects in GameData");
	}

	private void SaveCustomLayerDataToGameData()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			resourcesManager.SaveCustomLayerData(_customDataManager);
		}
	}

	private void RestoreObjectHealth(Vector2I tilePosition, IDestroyableObject destroyableObject)
	{
		if (_objectHealthData.ContainsKey(tilePosition))
		{
			int savedHealth = _objectHealthData[tilePosition];
			destroyableObject.SetCurrentHealth(savedHealth);
		}
	}

	private Tree2 SpawnTree(Vector2I tilePosition)
	{
		if (Tree2Scene == null)
		{
			Tree2Scene = GD.Load<PackedScene>("res://scenes/mapObjects/Tree2.tscn");
			if (Tree2Scene == null)
			{
				GD.PrintErr("Region6Manager: Failed to load Tree2 scene!");
				return null;
			}
		}

		var tree = Tree2Scene.Instantiate<Tree2>();
		if (tree == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate tree!");
			return null;
		}

		tree.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", tree);
		tree.Tree2Destroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, tree);

		return tree;
	}

	private Rock SpawnRock(Vector2I tilePosition)
	{
		if (RockScene == null)
		{
			RockScene = GD.Load<PackedScene>("res://scenes/mapObjects/Rock.tscn");
			if (RockScene == null)
			{
				GD.PrintErr("Region6Manager: Failed to load Rock scene!");
				return null;
			}
		}

		var rock = RockScene.Instantiate<Rock>();
		if (rock == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate rock!");
			return null;
		}

		rock.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", rock);
		rock.RockDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, rock);

		return rock;
	}

	private BerryBush SpawnBerryBush(Vector2I tilePosition)
	{
		if (BerryBushScene == null)
		{
			BerryBushScene = GD.Load<PackedScene>("res://scenes/mapObjects/BerryBush.tscn");
			if (BerryBushScene == null)
			{
				GD.PrintErr("Region6Manager: Failed to load BerryBush scene!");
				return null;
			}
		}

		var berryBush = BerryBushScene.Instantiate<BerryBush>();
		if (berryBush == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate berry bush!");
			return null;
		}

		berryBush.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", berryBush);
		berryBush.BerryBushDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, berryBush);

		return berryBush;
	}

	private GreenBush2 SpawnGreenBush(Vector2I tilePosition)
	{
		if (GreenBush2Scene == null)
		{
			GreenBush2Scene = GD.Load<PackedScene>("res://scenes/mapObjects/GreenBush2.tscn");
			if (GreenBush2Scene == null)
			{
				GD.PrintErr("Region6Manager: Failed to load GreenBush2 scene!");
				return null;
			}
		}

		var greenBush = GreenBush2Scene.Instantiate<GreenBush2>();
		if (greenBush == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate green bush!");
			return null;
		}

		greenBush.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", greenBush);
		greenBush.GreenBush2Destroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, greenBush);

		return greenBush;
	}

	private Rock2 SpawnRock2(Vector2I tilePosition)
	{
		if (Rock2Scene == null)
		{
			Rock2Scene = GD.Load<PackedScene>("res://scenes/mapObjects/Rock2.tscn");
			if (Rock2Scene == null)
			{
				GD.PrintErr("Region6Manager: Failed to load Rock2 scene!");
				return null;
			}
		}

		var rock2 = Rock2Scene.Instantiate<Rock2>();
		if (rock2 == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate rock2!");
			return null;
		}

		rock2.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", rock2);
		rock2.Rock2Destroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, rock2);

		return rock2;
	}

	private CopperRock SpawnCopperRock(Vector2I tilePosition)
	{
		if (CopperRockScene == null)
		{
			CopperRockScene = GD.Load<PackedScene>("res://scenes/mapObjects/CopperRock.tscn");
			if (CopperRockScene == null)
			{
				GD.PrintErr("Region6Manager: Failed to load CopperRock scene!");
				return null;
			}
		}

		var copperRock = CopperRockScene.Instantiate<CopperRock>();
		if (copperRock == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate copper rock!");
			return null;
		}

		copperRock.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", copperRock);
		copperRock.CopperRockDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, copperRock);

		return copperRock;
	}

	private BrownMushroom SpawnMushroom(Vector2I tilePosition)
	{
		if (BrownMushroomScene == null)
		{
			BrownMushroomScene = GD.Load<PackedScene>("res://scenes/mapObjects/BrownMushroom.tscn");
			if (BrownMushroomScene == null)
			{
				GD.PrintErr("Region6Manager: Failed to load BrownMushroom scene!");
				return null;
			}
		}

		var mushroom = BrownMushroomScene.Instantiate<BrownMushroom>();
		if (mushroom == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate mushroom!");
			return null;
		}

		mushroom.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", mushroom);
		mushroom.BrownMushroomDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, mushroom);

		return mushroom;
	}

	private Trunk SpawnTrunk(Vector2I tilePosition)
	{
		if (TrunkScene == null)
		{
			TrunkScene = GD.Load<PackedScene>("res://scenes/mapObjects/Trunk.tscn");
			if (TrunkScene == null)
			{
				GD.PrintErr("Region6Manager: Failed to load Trunk scene!");
				return null;
			}
		}

		var trunk = TrunkScene.Instantiate<Trunk>();
		if (trunk == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate trunk!");
			return null;
		}

		trunk.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", trunk);
		trunk.TrunkDestroyed += OnObjectDestroyed;
		RestoreObjectHealth(tilePosition, trunk);

		return trunk;
	}

	private void OnObjectDestroyed(Vector2I tilePosition)
	{
		if (_activeObjects.ContainsKey(tilePosition))
		{
			_activeObjects.Remove(tilePosition);
			_objectHealthData.Remove(tilePosition);
			UpdateObjectHealthInGameData();
		}
	}

	private void OnGraveyardCandleInteracted(int candleId)
	{
		if (_chestsSpawned) return;

		if (AreAllGraveyardCandlesLit())
		{
			SpawnChests();
			_chestsSpawned = true;
		}
	}

	private bool AreAllGraveyardCandlesLit()
	{
		var candleStates = GameSaveData.Instance.Region6CandleStates;

		if (candleStates.Count < 8) return false;

		for (int i = 0; i < 8; i++)
		{
			if (!candleStates[i])
			{
				return false;
			}
		}

		return true;
	}

	private void SpawnChests()
	{
		SpawnChest2();
		SpawnStandardChests();
		GD.Print("Region6Manager: All graveyard candles lit - spawned reward chests!");
	}

	private void SpawnChest2()
	{
		var chest2Scene = GD.Load<PackedScene>("res://scenes/mapObjects/Chest2.tscn");
		if (chest2Scene == null)
		{
			GD.PrintErr("Region6Manager: Failed to load Chest2 scene!");
			return;
		}

		var chest2 = chest2Scene.Instantiate<Chest2>();
		if (chest2 == null)
		{
			GD.PrintErr("Region6Manager: Failed to instantiate Chest2!");
			return;
		}

		Vector2 spawnPosition = new Vector2(6 * 16 + 8, -13 * 16 + 8);
		chest2.GlobalPosition = spawnPosition;

		var rewards = new List<ResourceReward>
		{
			new ResourceReward { ResourceType = ResourceType.CopperOre, Quantity = 20 }
		};
		chest2.SetRewards(100, rewards);

		GetParent().CallDeferred("add_child", chest2);
	}

	private void SpawnStandardChests()
	{
		var chestScene = GD.Load<PackedScene>("res://scenes/mapObjects/Chest.tscn");
		if (chestScene == null)
		{
			GD.PrintErr("Region6Manager: Failed to load Chest scene!");
			return;
		}

		var chest1 = chestScene.Instantiate<Chest>();
		if (chest1 != null)
		{
			Vector2 spawnPosition1 = new Vector2(5 * 16 + 8, -13 * 16 + 8);
			chest1.GlobalPosition = spawnPosition1;
			chest1.GoldAmount = 10;

			var rewards1 = new List<ResourceReward>
			{
				new ResourceReward { ResourceType = ResourceType.CopperBar, Quantity = 5 }
			};
			chest1.ResourceRewards = rewards1;

			GetParent().CallDeferred("add_child", chest1);
		}

		var chest2 = chestScene.Instantiate<Chest>();
		if (chest2 != null)
		{
			Vector2 spawnPosition2 = new Vector2(7 * 16 + 8, -13 * 16 + 8);
			chest2.GlobalPosition = spawnPosition2;
			chest2.GoldAmount = 25;

			var rewards2 = new List<ResourceReward>
			{
				new ResourceReward { ResourceType = ResourceType.CarrotSeedBag, Quantity = 5 }
			};
			chest2.ResourceRewards = rewards2;

			GetParent().CallDeferred("add_child", chest2);
		}
	}
}
